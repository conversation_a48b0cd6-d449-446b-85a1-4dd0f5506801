import re
import difflib
from typing import List, Dict, Any
from .config import logger

def normalize_question(question: str) -> str:
    """
    Normalize a question for comparison by removing punctuation, 
    converting to lowercase, and standardizing whitespace.
    """
    if not question:
        return ""
    
    # Remove punctuation and convert to lowercase
    normalized = re.sub(r'[^\w\s]', '', question.lower())
    
    # Normalize whitespace
    normalized = ' '.join(normalized.split())
    
    return normalized

def calculate_question_similarity(question1: str, question2: str) -> float:
    """
    Calculate similarity between two questions using string similarity.
    Returns a value between 0.0 (completely different) and 1.0 (identical).
    """
    if not question1 or not question2:
        return 0.0
    
    # Normalize both questions
    norm_q1 = normalize_question(question1)
    norm_q2 = normalize_question(question2)
    
    if not norm_q1 or not norm_q2:
        return 0.0
    
    # Use difflib's SequenceMatcher for similarity calculation
    similarity = difflib.SequenceMatcher(None, norm_q1, norm_q2).ratio()
    
    return similarity

def is_question_duplicate(new_question: str, asked_questions: List[str], threshold: float = 0.7) -> bool:
    """
    Check if a new question is too similar to any previously asked questions.
    
    Args:
        new_question: The question to check
        asked_questions: List of previously asked questions
        threshold: Similarity threshold (0.0-1.0) above which questions are considered duplicates
    
    Returns:
        True if the question is considered a duplicate, False otherwise
    """
    if not new_question or not asked_questions:
        return False
    
    for asked_question in asked_questions:
        similarity = calculate_question_similarity(new_question, asked_question)
        if similarity >= threshold:
            logger.info(f"Question similarity detected: {similarity:.2f} - '{new_question}' vs '{asked_question}'")
            return True
    
    return False

def extract_question_keywords(question: str) -> List[str]:
    """
    Extract key words from a question for semantic analysis.
    """
    if not question:
        return []
    
    # Common question words to exclude
    stop_words = {
        'what', 'how', 'why', 'when', 'where', 'who', 'which', 'can', 'could', 
        'would', 'should', 'do', 'does', 'did', 'is', 'are', 'was', 'were',
        'tell', 'me', 'about', 'your', 'you', 'the', 'a', 'an', 'and', 'or',
        'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from',
        'apa', 'bagaimana', 'mengapa', 'kapan', 'dimana', 'siapa', 'mana',
        'bisa', 'dapat', 'akan', 'harus', 'ceritakan', 'tentang', 'anda',
        'saya', 'yang', 'dan', 'atau', 'tetapi', 'di', 'ke', 'untuk', 'dari'
    }
    
    # Extract words and filter out stop words
    words = re.findall(r'\b\w+\b', question.lower())
    keywords = [word for word in words if word not in stop_words and len(word) > 2]
    
    return keywords

def get_question_semantic_similarity(question1: str, question2: str) -> float:
    """
    Calculate semantic similarity based on shared keywords.
    """
    keywords1 = set(extract_question_keywords(question1))
    keywords2 = set(extract_question_keywords(question2))
    
    if not keywords1 or not keywords2:
        return 0.0
    
    # Calculate Jaccard similarity (intersection over union)
    intersection = len(keywords1.intersection(keywords2))
    union = len(keywords1.union(keywords2))
    
    return intersection / union if union > 0 else 0.0

def is_question_semantically_similar(new_question: str, asked_questions: List[str], threshold: float = 0.5) -> bool:
    """
    Check if a question is semantically similar to previously asked questions.
    """
    if not new_question or not asked_questions:
        return False
    
    for asked_question in asked_questions:
        semantic_similarity = get_question_semantic_similarity(new_question, asked_question)
        if semantic_similarity >= threshold:
            logger.info(f"Semantic similarity detected: {semantic_similarity:.2f} - '{new_question}' vs '{asked_question}'")
            return True
    
    return False

def validate_question_uniqueness(new_question: str, session_state: Dict[str, Any]) -> bool:
    """
    Comprehensive validation to ensure question uniqueness.
    
    Args:
        new_question: The question to validate
        session_state: Current session state containing asked questions and thresholds
    
    Returns:
        True if the question is unique enough, False if it's too similar to previous questions
    """
    asked_questions = session_state.get("asked_questions", [])
    similarity_threshold = session_state.get("question_similarity_threshold", 0.7)
    
    # Check string similarity
    if is_question_duplicate(new_question, asked_questions, similarity_threshold):
        return False
    
    # Check semantic similarity with a lower threshold
    if is_question_semantically_similar(new_question, asked_questions, 0.5):
        return False
    
    return True

def add_question_to_history(question: str, category: str, session_state: Dict[str, Any]) -> None:
    """
    Add a question to the session history for tracking.
    """
    if "asked_questions" not in session_state:
        session_state["asked_questions"] = []
    
    if "category_question_count" not in session_state:
        session_state["category_question_count"] = {}
    
    # Add to asked questions list
    session_state["asked_questions"].append(question)
    
    # Update category count
    if category in session_state["category_question_count"]:
        session_state["category_question_count"][category] += 1
    else:
        session_state["category_question_count"][category] = 1
    
    logger.info(f"Added question to history. Total questions: {len(session_state['asked_questions'])}")

def format_conversation_history(transcript: List[Dict[str, Any]], max_entries: int = 10) -> str:
    """
    Format conversation history for inclusion in AI prompts.
    
    Args:
        transcript: List of transcript entries
        max_entries: Maximum number of recent entries to include
    
    Returns:
        Formatted conversation history string
    """
    if not transcript:
        return "No previous conversation."
    
    # Get the most recent entries
    recent_entries = transcript[-max_entries:] if len(transcript) > max_entries else transcript
    
    formatted_history = []
    for entry in recent_entries:
        role = entry.get('role', 'unknown')
        content = entry.get('content', '')
        timestamp = entry.get('timestamp', '')
        
        if role and content:
            # Format role for better readability
            role_display = "INTERVIEWER" if role == "interviewer" else "CANDIDATE"
            formatted_history.append(f"{role_display}: {content}")
    
    return "\n".join(formatted_history)

def get_category_distribution(session_state: Dict[str, Any]) -> Dict[str, int]:
    """
    Get the distribution of questions asked per category.
    """
    return session_state.get("category_question_count", {})

def suggest_next_category(session_state: Dict[str, Any]) -> str:
    """
    Suggest the next category to ask questions from based on distribution and availability.
    """
    available_categories = session_state.get("question_categories", [])
    category_counts = get_category_distribution(session_state)
    last_category = session_state.get("last_category")
    
    if not available_categories:
        return "personal"  # Default fallback
    
    # Filter out the last category if possible
    candidate_categories = [cat for cat in available_categories if cat != last_category]
    if not candidate_categories:
        candidate_categories = available_categories
    
    # Find the category with the least questions asked
    min_count = float('inf')
    suggested_category = candidate_categories[0]
    
    for category in candidate_categories:
        count = category_counts.get(category, 0)
        if count < min_count:
            min_count = count
            suggested_category = category
    
    return suggested_category
